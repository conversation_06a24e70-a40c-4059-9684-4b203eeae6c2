<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Travel Agency CEO Dashboard</title>
    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <!-- Font Awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <style>
      :root {
        --primary-color: #3498db;
        --secondary-color: #2ecc71;
        --danger-color: #e74c3c;
        --warning-color: #f39c12;
      }
      body {
        background-color: #f8f9fa;
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
      }
      .sidebar {
        background-color: #343a40;
        color: white;
        min-height: 100vh;
      }
      .sidebar .nav-link {
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 5px;
      }
      .sidebar .nav-link:hover {
        color: white;
        background-color: rgba(255, 255, 255, 0.1);
      }
      .sidebar .nav-link.active {
        color: white;
        background-color: var(--primary-color);
      }
      .card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
      }
      .card:hover {
        transform: translateY(-5px);
      }
      .card-icon {
        font-size: 2rem;
        opacity: 0.7;
      }
      .kpi-card-1 {
        background-color: #e3f2fd;
      }
      .kpi-card-2 {
        background-color: #e8f5e9;
      }
      .kpi-card-3 {
        background-color: #fff3e0;
      }
      .kpi-card-4 {
        background-color: #fce4ec;
      }
      .chart-container {
        position: relative;
        height: 300px;
      }
    </style>
  </head>
  <body>
    <div class="container-fluid">
      <div class="row">
        <!-- Sidebar -->
        <div class="col-md-2 sidebar p-0">
          <div class="p-3">
            <h4 class="text-center mb-4">TravelEase</h4>
            <ul class="nav flex-column">
              <li class="nav-item">
                <a class="nav-link active" href="#"
                  ><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a
                >
              </li>
              <li class="nav-item">
                <a class="nav-link" href="#"
                  ><i class="fas fa-plane me-2"></i>Bookings</a
                >
              </li>
              <li class="nav-item">
                <a class="nav-link" href="#"
                  ><i class="fas fa-users me-2"></i>Customers</a
                >
              </li>
              <li class="nav-item">
                <a class="nav-link" href="#"
                  ><i class="fas fa-chart-line me-2"></i>Analytics</a
                >
              </li>
              <li class="nav-item">
                <a class="nav-link" href="#"
                  ><i class="fas fa-cog me-2"></i>Settings</a
                >
              </li>
            </ul>
          </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-10 p-4">
          <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>CEO Dashboard</h2>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle"
                type="button"
                id="timePeriodDropdown"
                data-bs-toggle="dropdown"
              >
                Last 30 Days
              </button>
              <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#">Last 7 Days</a></li>
                <li><a class="dropdown-item" href="#">Last 30 Days</a></li>
                <li><a class="dropdown-item" href="#">Last Quarter</a></li>
              </ul>
            </div>
          </div>

          <!-- KPI Cards -->
          <div class="row mb-4">
            <div class="col-md-3">
              <div class="card kpi-card-1">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h6 class="card-subtitle mb-2 text-muted">
                        Total Bookings
                      </h6>
                      <h3 class="card-title">1,248</h3>
                      <p class="card-text text-success">
                        <i class="fas fa-arrow-up"></i> 12% from last month
                      </p>
                    </div>
                    <i class="fas fa-calendar-check card-icon text-primary"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="card kpi-card-2">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h6 class="card-subtitle mb-2 text-muted">Revenue</h6>
                      <h3 class="card-title">$84,760</h3>
                      <p class="card-text text-success">
                        <i class="fas fa-arrow-up"></i> 8% from last month
                      </p>
                    </div>
                    <i class="fas fa-dollar-sign card-icon text-success"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="card kpi-card-3">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h6 class="card-subtitle mb-2 text-muted">
                        New Customers
                      </h6>
                      <h3 class="card-title">324</h3>
                      <p class="card-text text-danger">
                        <i class="fas fa-arrow-down"></i> 5% from last month
                      </p>
                    </div>
                    <i class="fas fa-user-plus card-icon text-warning"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="card kpi-card-4">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h6 class="card-subtitle mb-2 text-muted">
                        Cancellation Rate
                      </h6>
                      <h3 class="card-title">7.2%</h3>
                      <p class="card-text text-success">
                        <i class="fas fa-arrow-down"></i> 2% from last month
                      </p>
                    </div>
                    <i class="fas fa-times-circle card-icon text-danger"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Charts Row 1 -->
          <div class="row mb-4">
            <div class="col-md-8">
              <div class="card">
                <div class="card-body">
                  <h5 class="card-title">Revenue Trend (Last 6 Months)</h5>
                  <div class="chart-container">
                    <canvas id="revenueChart"></canvas>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="card">
                <div class="card-body">
                  <h5 class="card-title">Booking Sources</h5>
                  <div class="chart-container">
                    <canvas id="bookingSourceChart"></canvas>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Charts Row 2 -->
          <div class="row">
            <div class="col-md-6">
              <div class="card">
                <div class="card-body">
                  <h5 class="card-title">Top Destinations</h5>
                  <div class="chart-container">
                    <canvas id="topDestinationsChart"></canvas>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="card">
                <div class="card-body">
                  <h5 class="card-title">Customer Satisfaction</h5>
                  <div class="chart-container">
                    <canvas id="satisfactionChart"></canvas>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script>
      // Revenue Trend Chart (Line Chart)
      const revenueCtx = document
        .getElementById("revenueChart")
        .getContext("2d");
      const revenueChart = new Chart(revenueCtx, {
        type: "line",
        data: {
          labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
          datasets: [
            {
              label: "Revenue ($)",
              data: [65000, 59000, 80000, 81000, 82000, 84760],
              borderColor: "rgba(75, 192, 192, 1)",
              backgroundColor: "rgba(75, 192, 192, 0.2)",
              tension: 0.3,
              fill: true,
            },
          ],
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: "top",
            },
          },
        },
      });

      // Booking Source Chart (Doughnut)
      const bookingSourceCtx = document
        .getElementById("bookingSourceChart")
        .getContext("2d");
      const bookingSourceChart = new Chart(bookingSourceCtx, {
        type: "doughnut",
        data: {
          labels: ["Website", "Mobile App", "Agent", "Walk-in"],
          datasets: [
            {
              data: [45, 30, 15, 10],
              backgroundColor: [
                "rgba(54, 162, 235, 0.7)",
                "rgba(255, 99, 132, 0.7)",
                "rgba(255, 206, 86, 0.7)",
                "rgba(75, 192, 192, 0.7)",
              ],
              borderWidth: 1,
            },
          ],
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: "right",
            },
          },
        },
      });

      // Top Destinations Chart (Bar)
      const topDestinationsCtx = document
        .getElementById("topDestinationsChart")
        .getContext("2d");
      const topDestinationsChart = new Chart(topDestinationsCtx, {
        type: "bar",
        data: {
          labels: ["Bali", "Thailand", "Malaysia", "Singapore", "Dubai"],
          datasets: [
            {
              label: "Bookings",
              data: [320, 280, 210, 180, 150],
              backgroundColor: "rgba(153, 102, 255, 0.7)",
              borderColor: "rgba(153, 102, 255, 1)",
              borderWidth: 1,
            },
          ],
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false,
            },
          },
        },
      });

      // Satisfaction Chart (Radar)
      const satisfactionCtx = document
        .getElementById("satisfactionChart")
        .getContext("2d");
      const satisfactionChart = new Chart(satisfactionCtx, {
        type: "radar",
        data: {
          labels: [
            "Service",
            "Price",
            "Communication",
            "Timing",
            "Flexibility",
          ],
          datasets: [
            {
              label: "Customer Ratings",
              data: [8.5, 7.2, 9.1, 8.3, 7.8],
              backgroundColor: "rgba(255, 159, 64, 0.2)",
              borderColor: "rgba(255, 159, 64, 1)",
              pointBackgroundColor: "rgba(255, 159, 64, 1)",
              pointBorderColor: "#fff",
              pointHoverBackgroundColor: "#fff",
              pointHoverBorderColor: "rgba(255, 159, 64, 1)",
            },
          ],
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            r: {
              angleLines: {
                display: true,
              },
              suggestedMin: 0,
              suggestedMax: 10,
            },
          },
        },
      });
    </script>
  </body>
</html>
