declare interface ResultSetHeader {
  constructor: {
    name: 'ResultSetHeader';
  };
  affectedRows: number;
  fieldCount: number;
  info: string;
  insertId: number;
  serverStatus: number;
  warningStatus: number;
  /**
   * @deprecated
   * `changedRows` is deprecated and might be removed in the future major release. Please use `affectedRows` property instead.
   */
  changedRows: number;
}

export { ResultSetHeader };
