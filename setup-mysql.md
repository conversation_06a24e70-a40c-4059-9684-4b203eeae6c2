# MySQL Setup Guide

## Quick MySQL Setup Instructions

### Option 1: Using MySQL Command Line

1. **Install MySQL** (if not already installed):
   - Windows: Download from https://dev.mysql.com/downloads/mysql/
   - macOS: `brew install mysql`
   - Ubuntu: `sudo apt install mysql-server`

2. **Start MySQL Service**:
   - Windows: Start MySQL service from Services panel
   - macOS: `brew services start mysql`
   - Ubuntu: `sudo systemctl start mysql`

3. **Connect to MySQL**:
   ```bash
   mysql -u root -p
   ```

4. **Create Database and User**:
   ```sql
   CREATE DATABASE travel_agency;
   CREATE USER 'travel_user'@'localhost' IDENTIFIED BY 'your_password';
   GRANT ALL PRIVILEGES ON travel_agency.* TO 'travel_user'@'localhost';
   FLUSH PRIVILEGES;
   EXIT;
   ```

5. **Import Schema and Data**:
   ```bash
   mysql -u travel_user -p travel_agency < database/schema.sql
   mysql -u travel_user -p travel_agency < database/sample_data.sql
   ```

6. **Update .env file**:
   ```env
   DB_HOST=localhost
   DB_USER=travel_user
   DB_PASSWORD=your_password
   DB_NAME=travel_agency
   DB_PORT=3306
   ```

### Option 2: Using MySQL Workbench (GUI)

1. **Download MySQL Workbench**: https://dev.mysql.com/downloads/workbench/

2. **Create Connection**:
   - Host: localhost
   - Port: 3306
   - Username: root (or your MySQL user)

3. **Create Database**:
   - Right-click in Navigator → Create Schema
   - Name: `travel_agency`

4. **Import SQL Files**:
   - File → Open SQL Script
   - Open `database/schema.sql`
   - Execute (⚡ button)
   - Repeat for `database/sample_data.sql`

### Option 3: Using Docker (Easiest)

1. **Create docker-compose.yml**:
   ```yaml
   version: '3.8'
   services:
     mysql:
       image: mysql:8.0
       environment:
         MYSQL_ROOT_PASSWORD: rootpassword
         MYSQL_DATABASE: travel_agency
         MYSQL_USER: travel_user
         MYSQL_PASSWORD: travel_password
       ports:
         - "3306:3306"
       volumes:
         - mysql_data:/var/lib/mysql
         - ./database:/docker-entrypoint-initdb.d
   
   volumes:
     mysql_data:
   ```

2. **Start MySQL Container**:
   ```bash
   docker-compose up -d
   ```

3. **Update .env file**:
   ```env
   DB_HOST=localhost
   DB_USER=travel_user
   DB_PASSWORD=travel_password
   DB_NAME=travel_agency
   DB_PORT=3306
   ```

### Verification

After setting up MySQL, restart your server:
```bash
npm start
```

You should see:
```
✅ Database connected successfully
🚀 Server running on http://localhost:3000
```

### Troubleshooting

**Connection Refused**:
- Check if MySQL service is running
- Verify port 3306 is not blocked

**Access Denied**:
- Check username/password in .env
- Ensure user has proper privileges

**Database Not Found**:
- Make sure `travel_agency` database exists
- Check database name in .env

**Sample Queries to Test**:
```sql
USE travel_agency;
SELECT COUNT(*) FROM bookings;
SELECT * FROM destinations;
SELECT SUM(total_amount) FROM bookings WHERE status != 'Cancelled';
```

### Next Steps

Once MySQL is connected:
1. The dashboard will show real data from the database
2. You can add more sample data or connect to existing data
3. Customize the queries in `routes/dashboard.js` for your needs
4. Add new tables and endpoints as needed
